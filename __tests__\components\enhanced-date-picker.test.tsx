import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { EnhancedDatePicker } from '@/components/enhanced-date-picker'

// Mock date-fns functions
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') {
      return date.toISOString().split('T')[0]
    }
    if (formatStr === 'EEEE, MMMM d, yyyy') {
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
    return date.toISOString()
  }),
  parse: jest.fn((dateStr, formatStr, referenceDate) => {
    const date = new Date(dateStr)
    return isNaN(date.getTime()) ? new Date('invalid') : date
  }),
  isValid: jest.fn((date) => !isNaN(date.getTime())),
  startOfMonth: jest.fn((date) => new Date(date.getFullYear(), date.getMonth(), 1)),
  endOfMonth: jest.fn((date) => new Date(date.getFullYear(), date.getMonth() + 1, 0)),
  getYear: jest.fn((date) => date.getFullYear()),
  getMonth: jest.fn((date) => date.getMonth()),
}))

// Mock UI components
jest.mock('@/components/ui/calendar', () => ({
  Calendar: ({ onSelect, selected }: any) => (
    <div data-testid="calendar">
      <button 
        onClick={() => onSelect(new Date('2024-01-15'))}
        data-testid="calendar-select"
      >
        Select Date
      </button>
      {selected && <div data-testid="selected-date">{selected.toISOString()}</div>}
    </div>
  ),
}))

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <div data-testid="select">
      <select 
        value={value} 
        onChange={(e) => onValueChange(e.target.value)}
        data-testid="select-input"
      >
        {children}
      </select>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ value, children }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: () => <div>Select Value</div>,
}))

jest.mock('@/components/ui/popover', () => ({
  Popover: ({ children, open, onOpenChange }: any) => (
    <div data-testid="popover" data-open={open}>
      {children}
    </div>
  ),
  PopoverContent: ({ children }: any) => <div data-testid="popover-content">{children}</div>,
  PopoverTrigger: ({ children }: any) => <div data-testid="popover-trigger">{children}</div>,
}))

describe('EnhancedDatePicker', () => {
  const mockOnChange = jest.fn()

  beforeEach(() => {
    mockOnChange.mockClear()
  })

  it('renders with label and input', () => {
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
      />
    )

    expect(screen.getByText('Test Date')).toBeInTheDocument()
    expect(screen.getByDisplayValue('')).toBeInTheDocument()
  })

  it('shows required indicator when required', () => {
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
        required
      />
    )

    expect(screen.getByText('Test Date *')).toBeInTheDocument()
  })

  it('handles manual date input', async () => {
    const user = userEvent.setup()
    
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
      />
    )

    const input = screen.getByDisplayValue('')
    await user.type(input, '2024-01-15')

    expect(mockOnChange).toHaveBeenCalled()
  })

  it('opens calendar popover when calendar button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
      />
    )

    const calendarButton = screen.getByLabelText('Open calendar')
    await user.click(calendarButton)

    expect(screen.getByTestId('popover')).toBeInTheDocument()
  })

  it('displays selected date information', () => {
    const testDate = new Date('2024-01-15')
    
    render(
      <EnhancedDatePicker
        label="Test Date"
        value={testDate}
        onChange={mockOnChange}
      />
    )

    expect(screen.getByText(/Selected:/)).toBeInTheDocument()
  })

  it('disables input and button when disabled prop is true', () => {
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
        disabled
      />
    )

    const input = screen.getByDisplayValue('')
    const button = screen.getByLabelText('Open calendar')

    expect(input).toBeDisabled()
    expect(button).toBeDisabled()
  })

  it('handles calendar date selection', async () => {
    const user = userEvent.setup()
    
    render(
      <EnhancedDatePicker
        label="Test Date"
        onChange={mockOnChange}
      />
    )

    const calendarButton = screen.getByLabelText('Open calendar')
    await user.click(calendarButton)

    const selectButton = screen.getByTestId('calendar-select')
    await user.click(selectButton)

    expect(mockOnChange).toHaveBeenCalled()
  })
})
