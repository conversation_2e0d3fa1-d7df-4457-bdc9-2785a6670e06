import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LocalFileUpload } from '@/components/local-file-upload'

// Mock tooltip components
jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: any) => <div>{children}</div>,
  Tooltip: ({ children }: any) => <div>{children}</div>,
  TooltipTrigger: ({ children }: any) => <div>{children}</div>,
  TooltipContent: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
}))

describe('LocalFileUpload Enhancements', () => {
  const mockOnFilesChange = jest.fn()

  beforeEach(() => {
    mockOnFilesChange.mockClear()
  })

  it('displays "Choose Files" button text instead of "Select Multiple Files"', () => {
    render(
      <LocalFileUpload
        onFilesChange={mockOnFilesChange}
        maxFiles={5}
      />
    )

    expect(screen.getByText('Choose Files')).toBeInTheDocument()
    expect(screen.queryByText('Select Multiple Files')).not.toBeInTheDocument()
  })

  it('supports multi-file selection through file input', () => {
    render(
      <LocalFileUpload
        onFilesChange={mockOnFilesChange}
        maxFiles={5}
      />
    )

    const fileInput = screen.getByLabelText('Select multiple certificate files')
    expect(fileInput).toHaveAttribute('multiple')
    expect(fileInput).toHaveAttribute('type', 'file')
  })

  it('handles multiple file selection correctly', async () => {
    render(
      <LocalFileUpload
        onFilesChange={mockOnFilesChange}
        maxFiles={5}
      />
    )

    const fileInput = screen.getByLabelText('Select multiple certificate files')
    
    // Create mock files
    const file1 = new File(['content1'], 'test1.pdf', { type: 'application/pdf' })
    const file2 = new File(['content2'], 'test2.jpg', { type: 'image/jpeg' })
    const file3 = new File(['content3'], 'test3.png', { type: 'image/png' })

    // Simulate selecting multiple files
    Object.defineProperty(fileInput, 'files', {
      value: [file1, file2, file3],
      writable: false,
    })

    fireEvent.change(fileInput)

    await waitFor(() => {
      expect(mockOnFilesChange).toHaveBeenCalled()
    })
  })

  it('supports drag and drop of multiple files', async () => {
    render(
      <LocalFileUpload
        onFilesChange={mockOnFilesChange}
        maxFiles={5}
      />
    )

    const dropZone = screen.getByTitle('Click to select multiple files or drag and drop them here')
    
    // Create mock files
    const file1 = new File(['content1'], 'test1.pdf', { type: 'application/pdf' })
    const file2 = new File(['content2'], 'test2.jpg', { type: 'image/jpeg' })

    // Simulate drag and drop
    const dataTransfer = {
      files: [file1, file2],
      items: [file1, file2],
      types: ['Files'],
    }

    fireEvent.dragEnter(dropZone, { dataTransfer })
    fireEvent.dragOver(dropZone, { dataTransfer })
    fireEvent.drop(dropZone, { dataTransfer })

    await waitFor(() => {
      expect(mockOnFilesChange).toHaveBeenCalled()
    })
  })

  it('shows improved reorder buttons with ChevronUp and ChevronDown icons', async () => {
    const mockFiles = [
      {
        id: '1',
        file: new File(['content1'], 'test1.pdf', { type: 'application/pdf' }),
        name: 'test1.pdf',
        size: 1000,
        type: 'application/pdf',
        lastModified: Date.now(),
      },
      {
        id: '2',
        file: new File(['content2'], 'test2.pdf', { type: 'application/pdf' }),
        name: 'test2.pdf',
        size: 2000,
        type: 'application/pdf',
        lastModified: Date.now(),
      },
    ]

    // Mock the component with pre-selected files
    const TestComponent = () => {
      const [files, setFiles] = React.useState(mockFiles)
      return (
        <LocalFileUpload
          onFilesChange={setFiles}
          maxFiles={5}
        />
      )
    }

    render(<TestComponent />)

    // Wait for files to be displayed
    await waitFor(() => {
      expect(screen.getByText('test1.pdf')).toBeInTheDocument()
      expect(screen.getByText('test2.pdf')).toBeInTheDocument()
    })

    // Check for move up/down buttons (they should be present for multiple files)
    const moveButtons = screen.getAllByRole('button')
    const moveUpButtons = moveButtons.filter(button => 
      button.querySelector('[data-testid="chevron-up"]') || 
      button.getAttribute('aria-label')?.includes('Move up')
    )
    const moveDownButtons = moveButtons.filter(button => 
      button.querySelector('[data-testid="chevron-down"]') || 
      button.getAttribute('aria-label')?.includes('Move down')
    )

    expect(moveUpButtons.length).toBeGreaterThan(0)
    expect(moveDownButtons.length).toBeGreaterThan(0)
  })

  it('shows tooltips for action buttons', async () => {
    const user = userEvent.setup()
    
    const mockFiles = [
      {
        id: '1',
        file: new File(['content1'], 'test1.pdf', { type: 'application/pdf' }),
        name: 'test1.pdf',
        size: 1000,
        type: 'application/pdf',
        lastModified: Date.now(),
      },
      {
        id: '2',
        file: new File(['content2'], 'test2.pdf', { type: 'application/pdf' }),
        name: 'test2.pdf',
        size: 2000,
        type: 'application/pdf',
        lastModified: Date.now(),
      },
    ]

    const TestComponent = () => {
      const [files, setFiles] = React.useState(mockFiles)
      return (
        <LocalFileUpload
          onFilesChange={setFiles}
          maxFiles={5}
        />
      )
    }

    render(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByText('test1.pdf')).toBeInTheDocument()
    })

    // Tooltips should be present in the DOM
    expect(screen.getByText('Move up')).toBeInTheDocument()
    expect(screen.getByText('Move down')).toBeInTheDocument()
    expect(screen.getAllByText('Remove file')).toHaveLength(2)
  })

  it('respects maximum file limit', async () => {
    render(
      <LocalFileUpload
        onFilesChange={mockOnFilesChange}
        maxFiles={2}
      />
    )

    const fileInput = screen.getByLabelText('Select multiple certificate files')
    
    // Try to select more files than the limit
    const file1 = new File(['content1'], 'test1.pdf', { type: 'application/pdf' })
    const file2 = new File(['content2'], 'test2.pdf', { type: 'application/pdf' })
    const file3 = new File(['content3'], 'test3.pdf', { type: 'application/pdf' })

    Object.defineProperty(fileInput, 'files', {
      value: [file1, file2, file3],
      writable: false,
    })

    fireEvent.change(fileInput)

    await waitFor(() => {
      // Should show error about maximum files
      expect(screen.getByText(/Maximum 2 files allowed/)).toBeInTheDocument()
    })
  })
})
