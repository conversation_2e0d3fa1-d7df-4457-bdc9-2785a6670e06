"use client";

import type React from "react";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Calendar,
  Save,
  ArrowLeft,
  FileText,
  Loader2,
  CheckCircle2,
} from "lucide-react";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  LocalFileUpload,
  type LocalFile,
} from "@/components/local-file-upload";
import { useUploadThing } from "@/lib/uploadthing";

export default function NewCertificatePage() {
  const router = useRouter();
  const [issuedDate, setIssuedDate] = useState<Date>();
  const [expiryDate, setExpiryDate] = useState<Date>();
  const [noExpiry, setNoExpiry] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [localFiles, setLocalFiles] = useState<LocalFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success" | "error"
  >("idle");

  // Initialize UploadThing hook
  const { startUpload, isUploading } = useUploadThing("certificateUploader", {
    onClientUploadComplete: (res) => {
      console.log("Upload completed:", res);
    },
    onUploadError: (error) => {
      console.error("Upload error:", error);
    },
  });

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();
  }, [router]);

  // Function to upload files to UploadThing using the proper hook
  const uploadFilesToUploadthing = async (
    files: LocalFile[]
  ): Promise<any[]> => {
    if (files.length === 0) return [];

    setUploadStatus("uploading");
    setUploadProgress(0);

    try {
      // Convert LocalFile[] to File[]
      const filesToUpload = files.map((localFile) => localFile.file);

      // Upload files using UploadThing hook
      const uploadResults = await startUpload(filesToUpload);

      if (!uploadResults || uploadResults.length === 0) {
        throw new Error("Upload failed - no results returned");
      }

      // Map results to our expected format
      const uploadedFiles = uploadResults.map((result, index) => ({
        url: result.url,
        name: result.name || files[index].name,
        size: result.size || files[index].size,
        type: result.type || files[index].type,
        key: result.key,
      }));

      setUploadProgress(100);
      setUploadStatus("success");
      return uploadedFiles;
    } catch (error) {
      setUploadStatus("error");
      console.error("File upload error:", error);
      throw new Error(
        `File upload failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    const formData = new FormData(e.currentTarget);

    // Validate required fields
    const name = formData.get("name") as string;
    const authority = formData.get("authority") as string;
    const number = formData.get("number") as string;

    if (!name || !authority || !number || !issuedDate) {
      setError("Please fill in all required fields.");
      setIsSubmitting(false);
      return;
    }

    try {
      // Upload files to Uploadthing first (deferred upload)
      let uploadedFiles: any[] = [];
      if (localFiles.length > 0) {
        try {
          uploadedFiles = await uploadFilesToUploadthing(localFiles);
        } catch (uploadError) {
          throw new Error(
            `File upload failed: ${
              uploadError instanceof Error
                ? uploadError.message
                : "Unknown error"
            }. Your files are preserved locally.`
          );
        }
      }

      // Prepare certificate data with multiple files
      const certificateData = {
        name,
        issuingAuthority: authority,
        certificateNumber: number,
        dateIssued: issuedDate.toISOString(),
        expiryDate: noExpiry ? null : expiryDate?.toISOString() || null,
        notes: (formData.get("notes") as string) || null,
        isFavorite: false,
        // Include uploaded files data for multi-file support
        files: uploadedFiles.map((file, index) => ({
          fileName: file.name,
          fileUrl: file.url,
          fileSize: file.size,
          fileType: file.type,
          uploadthingKey: file.key,
          uploadOrder: index,
        })),
      };

      const response = await fetch("/api/certificates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(certificateData),
      });

      if (!response.ok) {
        if (response.status === 401) {
          router.push("/login");
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create certificate");
      }

      // Success - redirect to certificates page
      router.push("/certificates");
    } catch (err) {
      console.error("Error creating certificate:", err);
      setError(
        err instanceof Error
          ? err.message
          : "An unexpected error occurred. Please try again. Your local files have been preserved."
      );
      setIsSubmitting(false);
      setUploadStatus("error");
      // Note: localFiles state is preserved, so user doesn't lose their file selections
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container max-w-4xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <Button variant="ghost" asChild className="w-fit">
            <Link href="/certificates" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Certificates
            </Link>
          </Button>

          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Add New Certificate
            </h1>
            <p className="text-lg text-muted-foreground">
              Create a new certificate record with document attachments
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        {uploadStatus === "uploading" && (
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="pt-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin text-primary" />
                  <span className="font-medium">Uploading files...</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
                <p className="text-sm text-muted-foreground">
                  {Math.round(uploadProgress)}% complete
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success Indicator */}
        {uploadStatus === "success" && (
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                <span className="font-medium text-green-800 dark:text-green-200">
                  Files uploaded successfully!
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Main Form */}
          <div className="grid gap-8 lg:grid-cols-3">
            {/* Certificate Information */}
            <div className="lg:col-span-2 space-y-6">
              <Card className="shadow-lg border-0 bg-card/50 backdrop-blur">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">
                        Certificate Information
                      </CardTitle>
                      <CardDescription>
                        Basic details about your certificate
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Certificate Name */}
                  <div className="space-y-3">
                    <Label htmlFor="name" className="text-base font-medium">
                      Certificate Name *
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="e.g. STCW Basic Safety Training"
                      required
                      className="h-12 text-base"
                    />
                  </div>

                  {/* Issuing Authority */}
                  <div className="space-y-3">
                    <Label
                      htmlFor="authority"
                      className="text-base font-medium"
                    >
                      Issuing Authority *
                    </Label>
                    <Input
                      id="authority"
                      name="authority"
                      placeholder="e.g. Maritime Safety Authority"
                      required
                      className="h-12 text-base"
                    />
                  </div>

                  {/* Certificate Number */}
                  <div className="space-y-3">
                    <Label htmlFor="number" className="text-base font-medium">
                      Certificate Number *
                    </Label>
                    <Input
                      id="number"
                      name="number"
                      placeholder="e.g. BST-2023-12345"
                      required
                      className="h-12 text-base"
                    />
                  </div>

                  {/* Date Fields */}
                  <div className="grid gap-6 md:grid-cols-2">
                    {/* Date Issued */}
                    <div className="space-y-3">
                      <Label
                        htmlFor="issued-date"
                        className="text-base font-medium"
                      >
                        Date Issued *
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal h-12 text-base"
                          >
                            <Calendar className="mr-3 h-5 w-5" />
                            {issuedDate
                              ? format(issuedDate, "PPP")
                              : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={issuedDate}
                            onSelect={setIssuedDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Expiry Date */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label
                          htmlFor="expiry-date"
                          className="text-base font-medium"
                        >
                          Expiry Date
                        </Label>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="noExpiry"
                            name="noExpiry"
                            checked={noExpiry}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setExpiryDate(undefined);
                              }
                              setNoExpiry(!!checked);
                            }}
                          />
                          <label
                            htmlFor="noExpiry"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            No Expiry
                          </label>
                        </div>
                      </div>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal h-12 text-base"
                            disabled={noExpiry}
                          >
                            <Calendar className="mr-3 h-5 w-5" />
                            {expiryDate
                              ? format(expiryDate, "PPP")
                              : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={expiryDate}
                            onSelect={setExpiryDate}
                            initialFocus
                            disabled={noExpiry}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="space-y-3">
                    <Label htmlFor="notes" className="text-base font-medium">
                      Notes/Remarks
                    </Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      placeholder="Add any additional information about this certificate"
                      className="min-h-[120px] text-base resize-none"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* File Upload Sidebar */}
            <div className="space-y-6">
              <Card className="shadow-lg border-0 bg-card/50 backdrop-blur">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500/10 rounded-lg">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">Documents</CardTitle>
                      <CardDescription>
                        Upload certificate files
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <LocalFileUpload
                    onFilesChange={setLocalFiles}
                    maxFiles={5}
                    disabled={isSubmitting}
                    className="w-full"
                  />
                </CardContent>
              </Card>

              {/* File Summary */}
              {localFiles.length > 0 && (
                <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      <h4 className="font-medium text-blue-800 dark:text-blue-200">
                        Ready to Upload
                      </h4>
                      <p className="text-sm text-blue-600 dark:text-blue-300">
                        {localFiles.length} file
                        {localFiles.length !== 1 ? "s" : ""} selected. Files
                        will be uploaded when you save the certificate.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          <Separator className="my-8" />

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <Link href="/certificates" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Cancel
              </Link>
            </Button>

            <Button
              type="submit"
              disabled={isSubmitting || uploadStatus === "uploading"}
              className="w-full sm:w-auto min-w-[200px] h-12 text-base"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  {uploadStatus === "uploading"
                    ? "Uploading Files..."
                    : "Creating Certificate..."}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-5 w-5" />
                  Create Certificate
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
